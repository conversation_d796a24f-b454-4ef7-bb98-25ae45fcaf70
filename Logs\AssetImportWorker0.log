Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-12T13:33:46Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Team/HappinessBox
-logFile
Logs/AssetImportWorker0.log
-srvPort
14182
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Team/HappinessBox
D:/Unity/Team/HappinessBox
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [3332]  Target information:

Player connection [3332]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3004479884 [EditorId] 3004479884 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [3332]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3004479884 [EditorId] 3004479884 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [3332] Host joined multi-casting on [***********:54997]...
Player connection [3332] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 25.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Team/HappinessBox/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:   Intel
    VRAM:     6107 MB
    Driver:   31.0.101.2130
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56232
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.011891 seconds.
- Loaded All Assemblies, in  1.682 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2665 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.074 seconds
Domain Reload Profiling: 5755ms
	BeginReloadAssembly (582ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (260ms)
	RebuildNativeTypeToScriptingClass (63ms)
	initialDomainReloadingComplete (120ms)
	LoadAllAssembliesAndSetupDomain (657ms)
		LoadAssemblies (581ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (649ms)
			TypeCache.Refresh (644ms)
				TypeCache.ScanAssembly (585ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (4074ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3924ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3119ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (217ms)
			ProcessInitializeOnLoadAttributes (409ms)
			ProcessInitializeOnLoadMethodAttributes (165ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.185 seconds
Refreshing native plugins compatible for Editor in 4.99 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.046 seconds
Domain Reload Profiling: 7223ms
	BeginReloadAssembly (562ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (242ms)
	RebuildNativeTypeToScriptingClass (47ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (2209ms)
		LoadAssemblies (1535ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1022ms)
			TypeCache.Refresh (709ms)
				TypeCache.ScanAssembly (629ms)
			BuildScriptInfoCaches (282ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (4048ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (37ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (410ms)
			ProcessInitializeOnLoadAttributes (2821ms)
			ProcessInitializeOnLoadMethodAttributes (223ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 5.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5837 unused Assets / (0.8 MB). Loaded Objects now: 6382.
Memory consumption went from 112.2 MB to 111.3 MB.
Total: 31.678100 ms (FindLiveObjects: 2.911800 ms CreateObjectMapping: 2.045500 ms MarkObjects: 22.026800 ms  DeleteObjects: 4.691900 ms)

========================================================================
Received Import Request.
  Time since last request: 606355.954842 seconds.
  path: Assets/Font/B Siavash_0 SDF.asset
  artifactKey: Guid(fdb7054d763031a448c4d0b35f0256d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Font/B Siavash_0 SDF.asset using Guid(fdb7054d763031a448c4d0b35f0256d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a72af8cb909e76b2bbfbef8c78de99ae') in 0.5282149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

