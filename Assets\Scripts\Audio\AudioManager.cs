using System.Collections;
using Data.Global;
using Logic.Global;
using Logic.MiniGame;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Logic.UI
{
    public class AudioManager : Singleton<AudioManager>
    {
        public UnityAction<bool, int> GetCurrentPlayingStatus { get; set; }

        [SerializeField] private PlayerTemplate playerTemplate;
        [SerializeField] private RectTransform spawnTransform;
        [SerializeField] private AudioSource audioSource, themeSource;
        private UniversalUIElement playerObject;
        private Button playButton, pauseButton, stopButton;
        private Slider playerBar;
        private int CurrentClipId;
        private bool isPaused = false;
        private const float SAFE_TIME = 0.001f;

        protected override void Awake()
        {
            GameDataManager.Instance.ClearData();
        }

        private IEnumerator Start()
        {
            yield return new WaitForSeconds(0.1f);
            Generate();
        }

        private void FixedUpdate()
        {
            if (playerBar != null && audioSource.clip != null)
            {
                playerBar.value = audioSource.time;

                if (!audioSource.isPlaying && !isPaused)
                {
                    Stop();
                    GetCurrentPlayingStatus.Invoke(false, CurrentClipId);
                }
            }
        }

        private void Generate()
        {
            playerObject = Instantiate(playerTemplate.prefab, spawnTransform).GetComponent<UniversalUIElement>();

            playButton = playerObject.GetUIComponent<Button>(playerTemplate.playButton);
            pauseButton = playerObject.GetUIComponent<Button>(playerTemplate.pauseButton);
            stopButton = playerObject.GetUIComponent<Button>(playerTemplate.stopButton);
            playerBar = playerObject.GetUIComponent<Slider>(playerTemplate.playerBar);

            playButton.onClick.AddListener(Play);
            pauseButton.onClick.AddListener(Pause);
            stopButton.onClick.AddListener(Stop);

            playerBar.onValueChanged.AddListener((value) =>
                {
                    if (audioSource.clip != null)
                    {
                        float validTime = Mathf.Clamp(value, 0, audioSource.clip.length - SAFE_TIME);
                        audioSource.time = validTime;
                    }
                });

            playerObject.gameObject.SetActive(false);
        }

        public void Play()
        {
            themeSource.Pause();
            GetCurrentPlayingStatus.Invoke(true, CurrentClipId);
            playerObject.gameObject.SetActive(true);
            playButton.gameObject.SetActive(false);
            pauseButton.gameObject.SetActive(true);

            if (playerBar != null && audioSource.clip != null)
                playerBar.maxValue = audioSource.clip.length;

            audioSource.Play();
            isPaused = false;

            
        }

        public void Play(AudioClip audioClip)
        {
            audioSource.clip = audioClip;
            Play();
        }

        public void Play(AudioClip audioClip, int clipIndex)
        {
            audioSource.clip = audioClip;
            CurrentClipId = clipIndex;
            Play();
        }

        public void Pause()
        {
            if (!themeSource.isPlaying)
                themeSource.Play();

            GetCurrentPlayingStatus.Invoke(false, CurrentClipId);
            audioSource.Pause();
            playButton.gameObject.SetActive(true);
            pauseButton.gameObject.SetActive(false);
            isPaused = true;
        }

        public void Stop()
        {
            if (!themeSource.isPlaying)
                themeSource.Play();

            GetCurrentPlayingStatus.Invoke(false, CurrentClipId);
            audioSource.Stop();
            playerObject.gameObject.SetActive(false);
            playButton.gameObject.SetActive(true);
            pauseButton.gameObject.SetActive(false);
            isPaused = false;
        }
    }

    [System.Serializable]
    public class PlayerTemplate
    {
        public GameObject prefab;
        public EmptyVariable playButton;
        public EmptyVariable pauseButton;
        public EmptyVariable stopButton;
        public EmptyVariable playerBar;
    }
}