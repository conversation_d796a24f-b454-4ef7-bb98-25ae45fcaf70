using System.Collections.Generic;
using Data.Global;
using Logic.Global;
using Logic.MiniGame;
using UnityEngine;
using UnityEngine.Localization.Components;
using UnityEngine.UI;

namespace Logic.UI
{
    public class AudioGenerator : Singleton<AudioGenerator>
    {
        [SerializeField] private int columns, rows;
        [SerializeField] private List<AudioObject> data;
        [SerializeField] private SongTemplate songTemplate;
        [SerializeField] private EmptyVariable[] menuObjectIds;

        public void Generate()
        {
            GenerateUI();
        }

        private void GenerateUI()
        {
            foreach (var categoryObject in menuObjectIds)
            {
                AudioData audioData = null;
                foreach (var audioObj in data)
                {
                    audioData = audioObj.audioData.Find(x => x.categoryId == categoryObject);

                    if (audioData != null)
                        break;
                }

                if (audioData == null)
                {
                    print($"Can't find associated menu for {categoryObject.name}");
                    return;
                }

                for (int i = 0; i < audioData.audios.Length; i++)
                {
                    RectTransform menu = MenuGenerator.Instance.GetGeneratedMenuContent(categoryObject);
                    FlexibleGridLayout gridLayout = menu.GetComponent<FlexibleGridLayout>();
                    gridLayout.columns = columns;
                    gridLayout.rows = rows;
                    AudioClipData clip = audioData.audios[i];
                    int clipIndex = i;

                    UniversalUIElement audioPlayer = Instantiate(songTemplate.prefab, menu).GetComponent<UniversalUIElement>();

                    SetupUIElement(audioPlayer, clip, clipIndex);
                }
            }
        }

        private void SetupUIElement(UniversalUIElement audioPlayer, AudioClipData clipData, int index)
        {
            if (audioPlayer != null)
            {
                Button playButton = audioPlayer.GetUIComponent<Button>(songTemplate.playButtonId);
                Button pauseButton = audioPlayer.GetUIComponent<Button>(songTemplate.pauseButtonId);
                LocalizeStringEvent title = audioPlayer.GetUIComponent<LocalizeStringEvent>(songTemplate.titleId);
                Image icon = audioPlayer.GetUIComponent<Image>(songTemplate.iconId);

                title.StringReference = clipData.title;
                icon.sprite = clipData.icon;

                playButton.onClick.AddListener(delegate
                {
                    AudioManager.Instance.Play(clipData.clip, index);
                    RectTransformAnimation animation = audioPlayer.GetComponentInParent<RectTransformAnimation>();
                    animation.Play();
                });
                // playButton.onClick.AddListener(() => AudioManager.Instance.Play(clipData.clip, index));
                // playButton.onClick.AddListener(() => ToggleButton(playButton, pauseButton, true));

                pauseButton.onClick.AddListener(() => AudioManager.Instance.Pause());
                // pauseButton.onClick.AddListener(() => ToggleButton(playButton, pauseButton, false));

                AudioManager.Instance.GetCurrentPlayingStatus += (isPlaying, currentClipIndex) => ToggleButton(playButton, pauseButton, isPlaying, currentClipIndex, index);

                Button miniGameButton = audioPlayer.GetUIComponent<Button>(songTemplate.miniGameButtonId);
                if (clipData.supportedMiniGames == MiniGameName.None)
                {
                    miniGameButton.interactable = false;
                    return;
                }
                miniGameButton.onClick.AddListener(delegate
                {
                    MenuGenerator.Instance.OpenMiniGamePanel(new List<string> { clipData.supportedMiniGames.ToString() });
                    GameDataManager.Instance.SelectedMiniGameData = clipData.miniGameData;
                });
            }
            else
            {
                Debug.LogError("Failed to create audio player UI element.");
            }
        }

        private void ToggleButton(Button playButton, Button pauseButton, bool play)
        {
            bool playToggle = play ? false : true;

            playButton.gameObject.SetActive(playToggle);
            pauseButton.gameObject.SetActive(!playToggle);
        }

        private void ToggleButton(Button playButton, Button pauseButton, bool play, int currentClipIndex, int clipIndex)
        {
            bool playStatus = currentClipIndex == clipIndex ? play : false;
            ToggleButton(playButton, pauseButton, playStatus);
        }
    }

    [System.Serializable]
    public class SongTemplate
    {
        public GameObject prefab;
        public EmptyVariable iconId;
        public EmptyVariable playButtonId;
        public EmptyVariable pauseButtonId;
        public EmptyVariable titleId;
        public EmptyVariable miniGameButtonId;
    }
}