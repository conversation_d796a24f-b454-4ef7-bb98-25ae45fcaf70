using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.Events;

namespace Logic.Global
{
    /// <summary>
    /// <PERSON>les checking for and downloading updated addressable assets from the server
    /// </summary>
    public class DownloadHandler : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private Slider progressBar;
        [SerializeField] private TextMeshProUGUI progressText;
        [SerializeField] private TextMeshProUGUI downloadSizeText;
        [SerializeField] private GameObject downloadPanel;

        [Header("Settings")]
        [SerializeField] private List<string> keysToDownload = new();
        [SerializeField] private bool checkOnStart = true;

        // Events
        public event UnityAction OnDownloadComplete;
        public event UnityAction<string> OnDownloadFailed;
        public event UnityAction<bool> OnCatalogUpdated;

        private readonly AddressablesHelper helper = new();

        private void Start()
        {
            if (checkOnStart)
            {
                StartCoroutine(UpdateAddressablesAndDownload());
            }
        }

        /// <summary>
        /// Manually trigger a check for addressable updates and download content
        /// </summary>
        public void CheckForUpdatesManually()
        {
            StartCoroutine(UpdateAddressablesAndDownload());
        }

        /// <summary>
        /// Checks if the addressable catalog has updates and downloads content if needed
        /// </summary>
        private IEnumerator UpdateAddressablesAndDownload()
        {
            // First, update the catalogs
            var updateCatalogsTask = helper.UpdateCatalogsAsync(null, result =>
            {
                if (!result.IsSuccess)
                {
                    Debug.LogError($"Catalog update failed: {result.Exception?.Message}");
                }
            });
            yield return new WaitUntil(() => updateCatalogsTask.GetAwaiter().IsCompleted);

            bool catalogUpdateSuccess = updateCatalogsTask.GetAwaiter().GetResult();
            OnCatalogUpdated?.Invoke(catalogUpdateSuccess);

            if (!catalogUpdateSuccess)
            {
                Debug.LogWarning("Catalog update failed, but continuing with download attempt.");
            }

            // Now proceed with downloading content
            if (keysToDownload.Count == 0)
            {
                Debug.LogWarning("No keys specified for download. Add keys to the keysToDownload list.");
                yield break;
            }

            // Show the download panel
            if (downloadPanel != null)
                downloadPanel.SetActive(true);

            // Calculate total download size for all keys
            long totalDownloadSize = 0;
            bool downloadSizeError = false;

            foreach (string key in keysToDownload)
            {
                var sizeTask = helper.GetDownloadSizeAsync(key, result =>
                {
                    if (!result.IsSuccess)
                    {
                        Debug.LogError($"Failed to get download size for key '{key}': {result.Exception?.Message}");
                        downloadSizeError = true;
                    }
                });

                yield return new WaitUntil(() => sizeTask.GetAwaiter().IsCompleted);

                if (!downloadSizeError)
                {
                    totalDownloadSize += sizeTask.GetAwaiter().GetResult();
                }
            }

            if (downloadSizeError)
            {
                OnDownloadFailed?.Invoke("Failed to get download size for one or more keys");
                if (downloadPanel != null)
                    downloadPanel.SetActive(false);
                yield break;
            }

            // If there's nothing to download, we're done
            if (totalDownloadSize <= 0)
            {
                Debug.Log("No new content to download.");
                if (downloadPanel != null)
                    downloadPanel.SetActive(false);
                OnDownloadComplete?.Invoke();
                yield break;
            }

            // Display the download size
            if (downloadSizeText != null)
            {
                string sizeText = FormatFileSize(totalDownloadSize);
                downloadSizeText.text = $"Download size: {sizeText}";
            }

            Debug.Log($"Starting download of {FormatFileSize(totalDownloadSize)}");

            // Download dependencies for each key with progress tracking
            int completedKeys = 0;
            bool downloadError = false;

            foreach (string key in keysToDownload)
            {
                var downloadTask = helper.PreloadDependenciesAsync(key,
                    progress =>
                    {
                        // Calculate overall progress
                        float keyProgress = progress / keysToDownload.Count;
                        float overallProgress = (completedKeys / (float)keysToDownload.Count) + keyProgress;
                        UpdateProgressUI(overallProgress, totalDownloadSize);
                    },
                    result =>
                    {
                        if (!result.IsSuccess)
                        {
                            Debug.LogError($"Failed to download dependencies for key '{key}': {result.Exception?.Message}");
                            downloadError = true;
                        }
                    });

                yield return new WaitUntil(() => downloadTask.GetAwaiter().IsCompleted);

                if (downloadError)
                {
                    OnDownloadFailed?.Invoke($"Failed to download dependencies for key '{key}'");
                    if (downloadPanel != null)
                        downloadPanel.SetActive(false);
                    yield break;
                }

                completedKeys++;
            }

            // Final progress update
            UpdateProgressUI(1f, totalDownloadSize);
            Debug.Log("Download completed successfully");
            OnDownloadComplete?.Invoke();

            // Hide the download panel after a short delay
            yield return new WaitForSeconds(1f);
            if (downloadPanel != null)
                downloadPanel.SetActive(false);
        }

        /// <summary>
        /// Updates the progress UI elements
        /// </summary>
        private void UpdateProgressUI(float progress, long totalSize)
        {
            if (progressBar != null)
                progressBar.value = progress;

            if (progressText != null)
            {
                string percentage = (progress * 100f).ToString("F0");
                string downloadedSize = FormatFileSize((long)(totalSize * progress));
                string totalSizeFormatted = FormatFileSize(totalSize);
                progressText.text = $"{percentage}% ({downloadedSize} / {totalSizeFormatted})";
            }
        }

        /// <summary>
        /// Formats a file size in bytes to a human-readable string
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        private void OnDestroy()
        {
            // Release all assets managed by the helper
            helper?.ReleaseAllAssets();
        }
    }
}